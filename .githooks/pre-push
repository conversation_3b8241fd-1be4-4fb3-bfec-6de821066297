 #!/bin/bash
set -e

# Add your custom logic here

# Read local and remote refs from stdin (Git passes them to pre-push)
while read local_ref local_sha remote_ref remote_sha; do

  # Handle different push cases
  if [ "$remote_sha" = "0000000000000000000000000000000000000000" ]; then
    echo "Remote branch does not exist yet — first push detected."
    # Get added/modified Python files staged for commit
    CHANGED_FILES=$(git diff --cached --name-only --diff-filter=AM -- '*.py')
  elif [ "$local_sha" = "$remote_sha" ]; then
    echo "No new commits to push."
    exit 0
  else
    # Normal push — only added or modified Python files
    CHANGED_FILES=$(git diff --name-only --diff-filter=AM "$remote_sha" "$local_sha" -- '*.py')
  fi

  if [ -z "$CHANGED_FILES" ]; then
    echo "No Python files changed."
    exit 0
  fi

  echo "Running code checks on Python files:"
  echo "$CHANGED_FILES"

  # Run Black (formatting)
  black $CHANGED_FILES --line-length 88

  # Run isort (imports)
  isort $CHANGED_FILES

done
